'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { MapPin, Navigation, Clock, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import PickableMap from '@/components/maps/PickableMap';
import useUserLocation from '@/hooks/useUserLocation';
import { orderService } from '@/services/api';
import { DeliveryAddress, PaymentMethod } from '@/types/order';
import { loadRazorpay } from '@/lib/loadRazorpay';
import { sendOrderConfirmationEmail } from '@/lib/emailjs';
import { getImageUrl } from '@/utils/imageUtils';
import { generateTempOrderId, extractOrderId } from '@/lib/orderUtils';
import { isWithinDeliveryArea } from '@/utils/distance';
import { toast } from 'sonner';

export default function OrderSummaryPage() {
  const router = useRouter();
  const { user } = useAuth();
  const {
    cart,
    loading: cartLoading,
    calculateCartTotals,
    clearCart,
    promoState,
    applyPromoCode,
    removePromoCode
  } = useCart();
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [promoCode, setPromoCode] = useState('');
  const [isValidatingPromo, setIsValidatingPromo] = useState(false);

  const {
    coordinates,
    formattedAddress,
    loading: locationLoading,
    error: locationError,
    getCurrentLocation,
    updateLocation
  } = useUserLocation();

  // Initialize location on page load if not available
  useEffect(() => {
    if (!coordinates && !locationLoading && !locationError) {
      console.log('OrderSummary: Initializing location...');
      getCurrentLocation();
    }
  }, [coordinates, locationLoading, locationError, getCurrentLocation]);

  // Calculate cart totals with promo code adjustments
  const cartTotals = calculateCartTotals();
  const discount = promoState.promoValidation?.discountAmount || 0;
  const adjustedDeliveryCharge = promoState.promoValidation?.isFreeDelivery ? 0 : cartTotals.deliveryCharge;
  const adjustedTotal = cartTotals.subtotal + cartTotals.platformFee + adjustedDeliveryCharge + cartTotals.tax - discount;

  // Handle promo code validation
  const handleApplyPromo = async (code: string) => {
    if (!code.trim()) return;

    setIsValidatingPromo(true);
    try {
      const success = await applyPromoCode(code, cartTotals.subtotal);
      setPromoCode('');

      if (success) {
        const validation = promoState.promoValidation;
        if (validation?.isFreeDelivery) {
          toast.success('Free delivery applied!', {
            description: validation.message
          });
        } else {
          toast.success('Promo code applied!', {
            description: validation?.message || `You saved ₹${validation?.discountAmount}!`
          });
        }
      } else {
        toast.error('Invalid promo code', {
          description: promoState.promoValidation?.message || 'Please enter a valid promo code.'
        });
      }
    } catch (error) {
      console.error('Error validating promo code:', error);
      toast.error('Error validating promo code', {
        description: 'Please try again later.'
      });
    } finally {
      setIsValidatingPromo(false);
    }
  };

  // Handle removing promo code
  const handleRemovePromo = () => {
    removePromoCode();
    setPromoCode('');
    toast.success('Promo code removed');
  };

  // Use user's saved address if available
  useEffect(() => {
    if (user?.location?.coordinates && !coordinates) {
      updateLocation(user.location.coordinates);
    }
  }, [user, coordinates, updateLocation]);

  const handleCreateOrder = async () => {
    if (!coordinates || !formattedAddress) {
      toast.error('Please select a delivery location');
      return;
    }

    // Check if location is within delivery area
    if (!isWithinDeliveryArea(coordinates)) {
      toast.error('Out of stock for your area', {
        description: 'Your delivery location is outside our service area. Please select a location within our delivery zone.',
        duration: 5000
      });
      return;
    }

    if (cart.items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    setIsCreatingOrder(true);

    try {
      // Create Google Maps URL
      const mapUrl = `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`;

      const deliveryAddress: DeliveryAddress = {
        formatted: formattedAddress,
        coordinates,
        mapUrl
      };

      // Always use Razorpay for payment processing
      await handleOnlinePayment(deliveryAddress);
    } catch (error) {
      console.error('Error creating order:', error);
      toast.error('Failed to create order. Please try again.');
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // Handle payments using Razorpay
  const handleOnlinePayment = async (deliveryAddress: DeliveryAddress) => {
    try {
      // Calculate the total amount to be paid (using adjusted total with promo code)
      const amount = adjustedTotal;

      // Create a Razorpay order
      const response = await fetch('/api/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment order');
      }

      const { orderId } = await response.json();

      // Load Razorpay SDK
      const isLoaded = await loadRazorpay();
      if (!isLoaded) {
        toast.error('Failed to load payment gateway. Please try again.');
        return;
      }

      // Configure Razorpay options
      const options: RazorpayCheckout.Options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,
        amount: amount * 100, // Amount in paise
        currency: 'INR',
        name: 'Bandiwala',
        description: 'Food Order Payment',
        order_id: orderId,
        handler: async (response: RazorpayCheckout.RazorpayResponse) => {
          // Verify the payment and create order in backend
          const verifyResponse = await fetch('/api/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              ...response,
              deliveryAddress,
              promoCode: promoState.appliedPromoCode || undefined
            }),
          });

          if (verifyResponse.ok) {
            try {
              // Payment verification and order creation handled by backend
              const verifyData = await verifyResponse.json();

              if (verifyData.success && verifyData.data.order) {
                console.log('Payment verified and order created:', verifyData.data.order);

              // Extract the actual order ID from the backend response
              const actualOrderId = verifyData.data.order._id;
              console.log('Extracted order ID:', actualOrderId);

              // Send confirmation email regardless of order creation success
              try {

                // Get EmailJS configuration
                const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
                const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
                const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
                const adminEmail = process.env.NEXT_PUBLIC_EMAILJS_ADMIN_EMAIL || '<EMAIL>';
                const userEmail = user?.email || '';

                // For testing: Force send to a specific email if user email is not available
                const testUserEmail = userEmail || '<EMAIL>'; // Fallback for testing

                console.log('EmailJS Config:', { serviceId, templateId, userId, adminEmail, userEmail });

                if (!serviceId || !templateId || !userId) {
                  throw new Error('EmailJS configuration is missing');
                }

                // Use order totals from backend response
                const order = verifyData.data.order;
                const subtotal = order.subtotal;
                const platformFee = order.platformFee;
                const deliveryCharge = order.deliveryCharge;
                const taxes = order.tax;
                const total = order.total;

                // Extract vendor information from cart items
                const vendors = cart.items.reduce((acc, item) => {
                  if (item.vendorName && !acc.find(v => v.name === item.vendorName)) {
                    acc.push({
                      name: item.vendorName,
                      phone: item.vendorPhone || 'Not available' // We'll need to add this to cart items
                    });
                  }
                  return acc;
                }, [] as Array<{name: string, phone: string}>);

                // Format items for the email - create a simple string representation
                const itemsText = cart.items.map(item =>
                  `${item.name} x${item.quantity} — ₹${(item.selectedSubcategory?.price || item.price || 0).toFixed(2)} (${item.vendorName || 'Unknown Vendor'})`
                ).join('\n');

                // Create Google Maps URL from coordinates
                const mapUrl = coordinates ?
                  `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}` :
                  '';

                // Get primary vendor info (from first item)
                const primaryVendor = vendors[0] || { name: 'Multiple Vendors', phone: 'Contact admin' };

                // Prepare the template parameters to match your Handlebars template
                const baseTemplateParams = {
                  // EmailJS standard parameters
                  from_name: 'Bandiwala Order System', // Sender name
                  reply_to: '<EMAIL>', // Reply-to address

                  // Order details matching your template variables
                  order_id: actualOrderId, // Use the extracted order ID
                  items_text: itemsText, // Plain text representation of items
                  address: formattedAddress,
                  coordinates: coordinates ? `${coordinates.lat}, ${coordinates.lng}` : '',
                  phone_number: user?.phone || 'Not provided',
                  map_url: mapUrl,
                  subtotal: subtotal.toFixed(2),
                  platform_fee: platformFee.toFixed(2),
                  delivery_charge: deliveryCharge.toFixed(2),
                  taxes: taxes.toFixed(2),
                  total: total.toFixed(2),

                  // Shop information
                  shop_name: primaryVendor.name,
                  shop_phone: primaryVendor.phone,

                  // Additional info
                  customer_name: user?.name || 'Customer',
                  customer_email: user?.email || 'No email provided',
                  order_date: new Date().toLocaleDateString('en-IN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })
                };

                // Import emailjs directly to ensure it's loaded
                const emailjs = (await import('@emailjs/browser')).default;

                // Initialize EmailJS
                emailjs.init(userId);

                // Send email to admin
                const adminTemplateParams = {
                  ...baseTemplateParams,
                  to_name: 'Bandiwala Admin',
                  recipient: adminEmail,
                  email: adminEmail,
                  to_email: adminEmail,
                };

                console.log('Sending admin email with params:', adminTemplateParams);
                const adminEmailResponse = await emailjs.send(
                  serviceId,
                  templateId,
                  adminTemplateParams
                );
                console.log('Admin email sent successfully:', adminEmailResponse);

                // Send email to user (always send to testUserEmail for testing)
                console.log('User email check:', { userEmail, testUserEmail, user: user?.email, hasUserEmail: !!userEmail });
                if (testUserEmail && testUserEmail.trim() !== '') {
                  const userTemplateParams = {
                    ...baseTemplateParams,
                    to_name: user?.name || 'Customer',
                    recipient: testUserEmail,
                    email: testUserEmail,
                    to_email: testUserEmail,
                  };

                  console.log('Sending user email with params:', userTemplateParams);
                  const userEmailResponse = await emailjs.send(
                    serviceId,
                    templateId,
                    userTemplateParams
                  );
                  console.log('User email sent successfully:', userEmailResponse);
                } else {
                  console.log('User email not available, skipping user email. User email:', userEmail);
                }

                console.log('Confirmation emails sent successfully');
              } catch (emailError) {
                console.error('Failed to send confirmation email:', emailError);
                // Log more detailed error information
                if (emailError instanceof Error) {
                  console.error('Error details:', {
                    name: emailError.name,
                    message: emailError.message,
                    stack: emailError.stack
                  });
                }
                // Don't block the order process if email fails
              }

              // Clear the cart after successful order
              try {
                await clearCart();
                console.log('Cart cleared successfully after order placement');
              } catch (clearCartError) {
                console.error('Failed to clear cart after order placement:', clearCartError);
                // Don't block the order process if cart clearing fails
              }

              // Navigate to the success page
              if (actualOrderId) {
                toast.success('Payment successful! Order placed.');
                router.push(`/checkout/success?orderId=${actualOrderId}`);
              } else {
                toast.success('Payment successful! However, there was an issue creating your order in our system. Our team will contact you shortly.');
                router.push('/checkout/success');
              }
              } else {
                throw new Error(verifyData.message || 'Payment verification failed');
              }
            } catch (verifyError) {
              console.error('Payment verification error:', verifyError);

              // Even if order creation fails, still try to send a confirmation email
              // since the payment was successful
              try {

                const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
                const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
                const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
                const toEmail = process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL || '<EMAIL>';

                if (serviceId && templateId && userId) {
                  // Generate a temporary order ID
                  const tempOrderId = generateTempOrderId();

                  // Import emailjs directly
                  const emailjs = (await import('@emailjs/browser')).default;
                  emailjs.init(userId);

                  // Calculate order totals for the email
                  const subtotal = cart.items.reduce((sum, item) => sum + ((item.selectedSubcategory?.price || item.price || 0) * item.quantity), 0);
                  const platformFee = 5; // Default platform fee
                  const deliveryCharge = 20; // Default delivery charge

                  // Calculate 5% tax on subtotal + platform fee + delivery charge (adjusted for promo)
                  const finalDeliveryCharge = promoState.promoValidation?.isFreeDelivery ? 0 : deliveryCharge;
                  const taxableAmount = subtotal + platformFee + finalDeliveryCharge;
                  const taxes = parseFloat((taxableAmount * 0.05).toFixed(2)); // 5% tax on all components with 2 decimal places
                  const total = subtotal + platformFee + finalDeliveryCharge + taxes - discount;

                  // Extract vendor information from cart items
                  const vendors = cart.items.reduce((acc, item) => {
                    if (item.vendorName && !acc.find(v => v.name === item.vendorName)) {
                      acc.push({
                        name: item.vendorName,
                        phone: item.vendorPhone || 'Not available'
                      });
                    }
                    return acc;
                  }, [] as Array<{name: string, phone: string}>);

                  // Format items for the email - create a simple string representation
                  const itemsText = cart.items.map(item =>
                    `${item.name} x${item.quantity} — ₹${(item.selectedSubcategory?.price || item.price || 0).toFixed(2)} (${item.vendorName || 'Unknown Vendor'})`
                  ).join('\n');

                  // Create Google Maps URL from coordinates
                  const mapUrl = coordinates ?
                    `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}` :
                    '';

                  // Get primary vendor info (from first item)
                  const primaryVendor = vendors[0] || { name: 'Multiple Vendors', phone: 'Contact admin' };

                  // Send a simplified email with parameters matching the template
                  await emailjs.send(
                    serviceId,
                    templateId,
                    {
                      to_name: 'Karthik',
                      from_name: 'Bandiwala Order System',
                      reply_to: '<EMAIL>',
                      recipient: toEmail,
                      email: toEmail,
                      to_email: toEmail,

                      // Order details matching your template variables
                      order_id: tempOrderId, // Use temporary order ID when order creation fails
                      items_text: itemsText, // Plain text representation of items
                      address: formattedAddress,
                      coordinates: coordinates ? `${coordinates.lat}, ${coordinates.lng}` : '',
                      phone_number: user?.phone || 'Not provided',
                      map_url: mapUrl,
                      subtotal: subtotal.toFixed(2),
                      platform_fee: platformFee.toFixed(2),
                      delivery_charge: deliveryCharge.toFixed(2),
                      taxes: taxes.toFixed(2),
                      total: total.toFixed(2),

                      // Shop information
                      shop_name: primaryVendor.name,
                      shop_phone: primaryVendor.phone,

                      // Additional info
                      customer_name: user?.name || 'Customer',
                      customer_email: user?.email || 'No email provided',
                      order_date: new Date().toLocaleDateString('en-IN')
                    }
                  );

                  console.log('Fallback email sent successfully');
                }
              } catch (fallbackEmailError) {
                console.error('Failed to send fallback email:', fallbackEmailError);
              }

              // Try to clear the cart even if order creation failed
              try {
                await clearCart();
                console.log('Cart cleared successfully after payment (fallback)');
              } catch (clearCartError) {
                console.error('Failed to clear cart after payment (fallback):', clearCartError);
              }

              toast.error('Payment verification failed. Please contact support.');
              router.push('/failure');
            }
          } else {
            console.error('Payment verification failed');
            toast.error('Payment verification failed');
            router.push('/failure');
          }
        },
        prefill: {
          name: user?.name || '',
          email: user?.email || '',
          contact: user?.phone || '',
        },
        theme: {
          color: '#f97316', // bandiwala-orange
        },
        modal: {
          ondismiss: function() {
            toast.info('Payment cancelled. You can try again.');
            setIsCreatingOrder(false);
          },
        },
      };

      // Initialize Razorpay
      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Failed to process payment. Please try again.');
    }
  };

  if (cartLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange"></div>
        </div>
      </div>
    );
  }

  if (cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <div className="mb-4">
            <Image
              src="/images/empty-cart.svg"
              alt="Empty Cart"
              width={200}
              height={200}
              className="mx-auto"
            />
          </div>
          <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">Add some delicious items to your cart and come back!</p>
          <Link href="/">
            <Button className="bg-bandiwala-orange hover:bg-bandiwala-red">
              Browse Menu
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      <div className="mb-6">
        <Link href="/cart" className="inline-flex items-center text-gray-600 hover:text-bandiwala-orange" replace={true}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Cart
        </Link>
        <h1 className="text-2xl font-bold mt-2">Order Summary</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Delivery Address</h2>

            <div className="mb-4">
              <div className="h-64 rounded-md overflow-hidden mb-4">
                <PickableMap
                  initialCoordinates={coordinates || { lat: 17.3850, lng: 78.4867 }}
                  onCoordinatesChange={updateLocation}
                  showCurrentLocationButton={true}
                  onGetCurrentLocation={getCurrentLocation}
                  isLoading={locationLoading}
                  height="100%"
                />
              </div>

              <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-md">
                <MapPin className="w-5 h-5 text-bandiwala-orange mt-0.5" />
                <div className="w-full">
                  <p className="font-medium">Delivery Location</p>
                  <p className="text-gray-600 text-sm">
                    {formattedAddress || 'Please select a location on the map'}
                  </p>

                  {coordinates && (
                    <div className="mt-2 space-y-1 text-xs text-gray-500">
                      <p>
                        <span className="font-medium">Coordinates:</span> {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                      </p>
                      <p className="break-all">
                        <span className="font-medium">Map URL:</span> {`https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`}
                      </p>
                      <p>
                        <a
                          href={`https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-bandiwala-orange hover:underline"
                        >
                          View on Google Maps
                        </a>
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {locationError && (
                <p className="text-sm text-red-600 mt-1">{locationError}</p>
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Order Items</h2>

            <div className="divide-y">
              {cart.items.map((item, index) => (
                <div key={`${item.menuItemId}-${index}`} className="py-4 flex gap-4">
                  <div className="w-16 h-16 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                    {item.image ? (
                      <Image
                        src={getImageUrl(item.image)}
                        alt={item.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-200">
                        <span className="text-gray-400">No image</span>
                      </div>
                    )}
                  </div>

                  <div className="flex-grow">
                    <div className="flex justify-between">
                      <h3 className="font-medium">{item.name}</h3>
                      <p className="font-medium">₹{((item.selectedSubcategory?.price || item.price || 0) * item.quantity).toFixed(2)}</p>
                    </div>
                    <p className="text-sm text-gray-600">
                      {item.selectedSubcategory?.title} • Qty: {item.quantity}
                    </p>
                    {item.notes && (
                      <p className="text-sm text-gray-500 mt-1">Note: {item.notes}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm p-6 sticky top-20">
            <h3 className="text-lg font-semibold mb-4">Order Summary</h3>

            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Clock size={16} />
              <span>Estimated delivery in 20-30 min</span>
            </div>

            {coordinates && formattedAddress && (
              <div className="mb-4 p-3 bg-gray-50 rounded-md text-xs">
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-bandiwala-orange mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-700">Delivery Address:</p>
                    <p className="text-gray-600 mb-1">{formattedAddress}</p>
                    <p className="text-gray-500">
                      <span className="font-medium">Coordinates:</span> {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                    </p>
                    <p className="text-gray-500 break-all">
                      <span className="font-medium">Map URL:</span> {`https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`}
                    </p>
                    <a
                      href={`https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-bandiwala-orange hover:underline text-xs"
                    >
                      View on Google Maps
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Promo Code Section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Promo Code</h3>
              {promoState.appliedPromoCode ? (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-green-700">{promoState.appliedPromoCode}</span>
                    <span className="text-xs text-green-600 ml-2">
                      {promoState.promoValidation?.message}
                    </span>
                  </div>
                  <button
                    onClick={handleRemovePromo}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </div>
              ) : (
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Enter promo code"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-bandiwala-orange focus:border-transparent"
                    disabled={isValidatingPromo}
                  />
                  <button
                    onClick={() => handleApplyPromo(promoCode)}
                    disabled={!promoCode.trim() || isValidatingPromo}
                    className="px-4 py-2 bg-bandiwala-orange text-white rounded-md text-sm hover:bg-bandiwala-red disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isValidatingPromo ? 'Validating...' : 'Apply'}
                  </button>
                </div>
              )}
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between text-sm">
                <span>Subtotal</span>
                <span>₹{cartTotals.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Platform fee</span>
                <span>₹{cartTotals.platformFee.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Delivery charge</span>
                <span className={promoState.promoValidation?.isFreeDelivery ? 'line-through text-gray-400' : ''}>
                  ₹{cartTotals.deliveryCharge.toFixed(2)}
                </span>
              </div>
              {promoState.promoValidation?.isFreeDelivery && (
                <div className="flex justify-between text-green-600 text-sm">
                  <span>Free delivery ({promoState.appliedPromoCode})</span>
                  <span>-₹{cartTotals.deliveryCharge.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Taxes</span>
                <span>₹{cartTotals.tax.toFixed(2)}</span>
              </div>
              {discount > 0 && !promoState.promoValidation?.isFreeDelivery && (
                <div className="flex justify-between text-green-600 text-sm">
                  <span>Discount{promoState.appliedPromoCode ? ` (${promoState.appliedPromoCode})` : ''}</span>
                  <span>-₹{discount.toFixed(2)}</span>
                </div>
              )}
              <div className="border-t pt-3 flex justify-between font-semibold">
                <span>Total</span>
                <span>₹{adjustedTotal.toFixed(2)}</span>
              </div>
            </div>

            {/* Location Warning */}
            {coordinates && !isWithinDeliveryArea(coordinates) && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-2">
                    <h4 className="text-xs font-medium text-red-800">
                      Out of delivery area
                    </h4>
                    <p className="mt-1 text-xs text-red-700">
                      Please select a location within our service area to proceed.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={handleCreateOrder}
              disabled={isCreatingOrder || !coordinates || !formattedAddress || (coordinates && !isWithinDeliveryArea(coordinates))}
              className="w-full bg-bandiwala-orange hover:bg-bandiwala-red"
            >
              {isCreatingOrder ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Processing...
                </>
              ) : (
                'Proceed to Pay'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Star, Clock, Truck, ShoppingBag } from 'lucide-react';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import ServiceButtons from '@/components/ServiceButtons';
import TrendingDishes from '@/components/TrendingDishes';
import MapSection from '@/components/MapSection';
import CTA from '@/components/CTA';
import Footer from '@/components/Footer';
import { vendorService } from '@/services/api';
import { getImageUrl } from '@/utils/imageUtils';
import { useAuth } from '@/contexts/AuthContext';

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  phone?: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

export default function Home() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendors = async () => {
      // Only fetch vendors if user is authenticated
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await vendorService.getAllVendors();
        if (response.success) {
          setVendors(response.data);
        } else {
          setError('Failed to fetch vendors');
        }
      } catch (err) {
        setError('An error occurred while fetching vendors');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    // Wait for auth to be determined before fetching
    if (!authLoading) {
      fetchVendors();
    }
  }, [isAuthenticated, authLoading]);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <Hero />
      <ServiceButtons />
      <section className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-8 sm:py-12">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            Featured <span className="text-bandiwala-orange">Vendors</span>
          </h2>
          <p className="text-gray-600 text-base sm:text-lg max-w-2xl mx-auto">
            Discover authentic flavors from our carefully selected local vendors
          </p>
        </div>

        {/* Show loading spinner while checking auth or fetching data */}
        {(authLoading || loading) && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-bandiwala-orange border-t-transparent"></div>
          </div>
        )}

        {/* Show login prompt when not authenticated */}
        {!authLoading && !isAuthenticated && (
          <div className="bg-gradient-to-r from-bandiwala-orange/10 to-bandiwala-red/10 border border-bandiwala-orange/20 rounded-xl p-8 max-w-md mx-auto text-center">
            <div className="mb-4">
              <ShoppingBag className="h-16 w-16 text-bandiwala-orange mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">Login Required</h3>
              <p className="text-gray-600 mb-6">
                Please log in to view our amazing vendors and start ordering delicious food!
              </p>
            </div>
            <div className="space-y-3">
              <Link href="/login">
                <button className="w-full bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-6 mb-3 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  Login to Continue
                </button>
              </Link>
              <Link href="/register">
                <button className="w-full border-2 border-bandiwala-orange text-bandiwala-orange hover:bg-bandiwala-orange hover:text-white font-semibold px-6 py-3 rounded-full transition-all duration-300">
                  Create Account
                </button>
              </Link>
            </div>
          </div>
        )}

        {/* Show error when authenticated but API fails */}
        {!authLoading && isAuthenticated && error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl relative max-w-md mx-auto" role="alert">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading vendors</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Show vendors only when authenticated and data is loaded */}
        {!authLoading && isAuthenticated && !loading && !error && vendors.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8">
            {vendors.map((vendor) => {
              // Log the vendor data for debugging
              console.log(`Home page - Rendering vendor card: ${vendor.name}, slug: ${vendor.slug}`);

              return (
                <Link href={`/vendors/${vendor.slug}`} key={vendor._id} prefetch={true}>
                  <div className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer transform hover:-translate-y-2 border border-gray-100">
                    {/* Image Container with Overlay - 75% of card height */}
                    <div className="relative h-64 sm:h-72 md:h-80 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                      {vendor.image && (
                        <Image
                          src={getImageUrl(vendor.image)}
                          alt={vendor.name}
                          width={400}
                          height={320}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      )}

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* Rating Badge */}
                      <div className="absolute top-3 left-3 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-full flex items-center shadow-lg">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                        <span className="text-sm font-semibold text-gray-800">{vendor.rating}</span>
                      </div>

                      {/* Delivery Info Badge */}
                      <div className="absolute top-3 right-3 bg-bandiwala-orange/90 backdrop-blur-sm text-white px-3 py-1.5 rounded-full flex items-center shadow-lg">
                        <Clock className="h-3 w-3 mr-1" />
                        <span className="text-xs font-medium">{vendor.deliveryTime}</span>
                      </div>
                    </div>

                    {/* Content - 25% of card height */}
                    <div className="p-4 sm:p-5">
                      <div className="mb-2">
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 line-clamp-1 group-hover:text-bandiwala-orange transition-colors duration-300">
                          {vendor.name}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-1 leading-relaxed">
                          {vendor.description}
                        </p>
                      </div>

                      {/* Bottom Info */}
                      <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                        <div className="flex items-center text-gray-600">
                          <Truck className="h-4 w-4 mr-1 text-bandiwala-orange" />
                          <span className="text-xs font-medium">
                            {vendor.deliveryFee === 0 ? 'Free Delivery' : `₹${vendor.deliveryFee} delivery`}
                          </span>
                        </div>

                        <div className="flex items-center text-gray-600">
                          <ShoppingBag className="h-4 w-4 mr-1 text-bandiwala-orange" />
                          <span className="text-xs font-medium">Min ₹{vendor.minOrderValue}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        )}

        {/* View All Vendors Button */}
        {!authLoading && isAuthenticated && !loading && !error && vendors.length > 0 && (
          <div className="text-center mt-10 sm:mt-12">
            <Link href="/vendors">
              <button className="bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                View All Vendors
              </button>
            </Link>
          </div>
        )}
      </section>

      {/* Only show these sections when authenticated */}
      {isAuthenticated && (
        <>
          <TrendingDishes />
          <MapSection />
        </>
      )}

      <CTA />
      <Footer />
    </div>
  );
}

'use client';

import { useState } from 'react';

export default function TestEmailVerification() {
  const [email, setEmail] = useState('<EMAIL>');
  const [phone, setPhone] = useState('+918688660055');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testEmailConnection = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing email connection...');
      
      // Test backend connection first
      const backendTestResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/test`);
      const backendTestData = await backendTestResponse.json();
      
      console.log('Backend test response:', backendTestData);

      // Test email sending by registering a user
      const registerResponse = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User',
          email: email,
          phone: phone,
          password: 'test123',
          verificationMethod: 'email'
        }),
      });

      const registerData = await registerResponse.json();
      console.log('Register response:', registerData);

      setResult({
        backendConnection: backendTestData,
        emailTest: registerData,
        timestamp: new Date().toISOString()
      });

    } catch (err) {
      console.error('Email test failed:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  const testResendOTP = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          phone: phone,
          verificationMethod: 'email'
        }),
      });

      const data = await response.json();
      console.log('Resend OTP response:', data);

      setResult(prev => ({
        ...prev,
        resendOTP: data,
        timestamp: new Date().toISOString()
      }));

    } catch (err) {
      console.error('Resend OTP failed:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  const testDirectEmail = async () => {
    setLoading(true);
    setError(null);

    try {
      const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/test-email`;
      console.log('Testing direct email at:', backendUrl);

      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email
        }),
      });

      const data = await response.json();
      console.log('Direct email test response:', data);

      setResult(prev => ({
        ...prev,
        directEmailTest: data,
        timestamp: new Date().toISOString()
      }));

    } catch (err) {
      console.error('Direct email test failed:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Email Verification Testing Tool
          </h1>

          <div className="space-y-6">
            {/* Test Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+918688660055"
                />
              </div>
            </div>

            {/* Test Buttons */}
            <div className="flex flex-wrap gap-4">
              <button
                onClick={testEmailConnection}
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Testing...' : 'Test Email Registration'}
              </button>

              <button
                onClick={testResendOTP}
                disabled={loading}
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Testing...' : 'Test Resend OTP'}
              </button>

              <button
                onClick={testDirectEmail}
                disabled={loading}
                className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Testing...' : 'Test Direct Email'}
              </button>
            </div>

            {/* Environment Info */}
            <div className="bg-gray-100 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Environment Configuration</h3>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">API URL:</span> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}
                </div>
                <div>
                  <span className="font-medium">Frontend URL:</span> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <strong>Error:</strong> {error}
              </div>
            )}

            {/* Results Display */}
            {result && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <h3 className="font-semibold mb-2">Test Results:</h3>
                <pre className="text-xs overflow-auto bg-white p-2 rounded border">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
              <h3 className="font-semibold mb-2">Instructions:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Make sure your backend server is running on port 6111</li>
                <li>Check the server console for email sending logs</li>
                <li>Verify your Gmail app password is correct in server/config.env</li>
                <li>Check your email inbox (including spam folder)</li>
                <li>Look for any error messages in the browser console</li>
              </ol>
            </div>

            {/* Troubleshooting */}
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
              <h3 className="font-semibold mb-2">Common Issues:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Gmail App Password not configured correctly</li>
                <li>2-Factor Authentication not enabled on Gmail</li>
                <li>SMTP settings incorrect in config.env</li>
                <li>Firewall blocking SMTP connections</li>
                <li>Email going to spam folder</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

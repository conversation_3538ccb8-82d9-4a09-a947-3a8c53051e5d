'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Header';
import Footer from '@/components/Footer';
import { vendorService } from '@/services/api';
import { useQuery } from '@tanstack/react-query';
import { getImageUrl } from '@/utils/imageUtils';
import { useAuth } from '@/contexts/AuthContext';
import { ShoppingBag } from 'lucide-react';


interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

export default function VendorsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data, isLoading, error } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await vendorService.getAllVendors();
      return response.data;
    },
    enabled: isAuthenticated // Only run query when authenticated
  });

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-bandiwala-orange border-t-transparent"></div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4 text-center">
          <ShoppingBag className="h-16 w-16 text-bandiwala-orange mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-6">
            Please log in to view our vendors and start ordering.
          </p>
          <Link href="/login">
            <button className="w-full bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-6 py-3 rounded-full transition-all duration-300">
              Login to Continue
            </button>
          </Link>
        </div>
      </div>
    );
  }

  console.log(data)
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar showBackButton={true} />
      <main className="flex-1">
        {/* <Profile /> */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <h2 className="text-2xl font-bold mb-6">Popular Vendors</h2>

            {isLoading && (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange"></div>
              </div>
            )}

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong className="font-bold">Error!</strong>
                <span className="block sm:inline"> Failed to load vendors. Please try again later.</span>
              </div>
            )}

            {data && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {data.map((vendor: Vendor) => {
                  // Log the vendor data for debugging
                  console.log(`Rendering vendor card: ${vendor.name}, slug: ${vendor.slug}`);

                  return (
                    <Link href={`/vendors/${vendor.slug}`} key={vendor._id} prefetch={true}>
                      <div className="bg-white rounded-lg shadow-lg overflow-hidden transform transition duration-200 hover:scale-105 cursor-pointer">
                        <div className="h-48 bg-gray-200 relative">
                          {vendor.image && (
                            <Image
                              src={getImageUrl(vendor.image)}
                              alt={vendor.name}
                              width={400}
                              height={192}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                        <div className="p-4">
                          <h3 className="text-lg font-semibold text-gray-900">{vendor.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{vendor.description}</p>
                          <div className="flex items-center mb-2">
                            <span className="text-yellow-400">★</span>
                            <span className="ml-1 text-sm text-gray-600">{vendor.rating}</span>
                          </div>
                          <div className="flex justify-between items-center text-sm text-gray-500">
                            <span>{vendor.deliveryTime}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Clock, ArrowRight } from 'lucide-react';
import bhaji from '@/assets/images/bhaji.jpg';
import { vendorService } from '@/services/api';
import { getImageUrl } from '@/utils/imageUtils';

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

const FeaturedVendors = () => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendors = async () => {
      try {
        setLoading(true);
        const response = await vendorService.getAllVendors();

        if (response.success && response.data) {
          setVendors(response.data);
        } else {
          setError('Failed to load vendors');
        }
      } catch (err) {
        console.error('Error fetching vendors:', err);
        setError('Failed to load vendors. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchVendors();
  }, []);

  return (
    <div className="section-container bg-gradient-to-br from-gray-50 to-gray-100 py-16">
      <div className="text-center mb-12">
        <h2 className="section-title text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Vendors</h2>
        <p className="section-subtitle text-lg text-gray-600 max-w-2xl mx-auto">
          Discover the most popular and trusted food vendors on Bandiwala
        </p>
      </div>

      {loading && (
        <div className="flex justify-center items-center py-16">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-bandiwala-orange border-t-transparent"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 text-red-700 px-6 py-4 rounded-lg my-8 max-w-2xl mx-auto" role="alert">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium">Error loading vendors</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {!loading && !error && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {vendors.map((vendor) => (
              <Link href={`/vendors/${vendor.slug}`} key={vendor._id} className="group">
                <Card className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white rounded-2xl h-full flex flex-col">
                  <div className="relative h-56 overflow-hidden">
                    <Image
                      src={getImageUrl(vendor.image)}
                      alt={vendor.name}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      width={400}
                      height={224}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                    <div className="absolute top-4 right-4 bg-gradient-to-r from-bandiwala-yellow to-yellow-400 px-3 py-1.5 rounded-full text-xs font-bold text-gray-800 shadow-lg">
                      ⭐ Featured
                    </div>
                    <div className="absolute top-4 left-4 bg-green-500 px-3 py-1.5 rounded-full text-xs font-bold text-white shadow-lg flex items-center">
                      <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                      Open Now
                    </div>
                  </div>

                  <CardContent className="p-6 flex-1 flex flex-col">
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="font-bold text-xl text-gray-900 group-hover:text-bandiwala-orange transition-colors duration-300 line-clamp-1">
                          {vendor.name}
                        </h3>
                      </div>

                      <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                        {vendor.description}
                      </p>

                      <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center bg-yellow-50 px-3 py-1.5 rounded-full">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                          <span className="text-sm font-bold text-gray-900">{vendor.rating}</span>
                          <span className="text-xs text-gray-500 ml-1">(4.2k)</span>
                        </div>

                        <div className="flex items-center text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
                          <Clock className="h-4 w-4 mr-1" />
                          <span className="text-xs font-medium">{vendor.deliveryTime}</span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center text-sm text-gray-600 mb-4">
                        <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                          Min: ₹{vendor.minOrderValue}
                        </span>
                        <span className="bg-green-50 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                          ₹{vendor.deliveryFee} delivery
                        </span>
                      </div>
                    </div>

                    <div className="mt-auto pt-4 border-t border-gray-100">
                      <div className="flex justify-between items-center group-hover:text-bandiwala-orange transition-colors duration-300">
                        <span className="text-sm font-bold text-bandiwala-red group-hover:text-bandiwala-orange">
                          View Menu
                        </span>
                        <ArrowRight className="h-4 w-4 text-bandiwala-red group-hover:text-bandiwala-orange transform group-hover:translate-x-1 transition-all duration-300" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>

          <div className="text-center mt-16">
            <Link href="/vendors">
              <button className="bg-gradient-to-r from-bandiwala-red to-bandiwala-orange hover:from-bandiwala-orange hover:to-bandiwala-red text-white border-0 px-8 py-4 rounded-full font-bold text-lg inline-flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                View All Vendors
                <ArrowRight className="ml-3 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </Link>
          </div>
        </>
      )}
    </div>
  );
};

export default FeaturedVendors;

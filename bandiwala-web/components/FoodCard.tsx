'use client';

import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Star, Plus, Minus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { getImageUrl } from '@/utils/imageUtils';
import { toast } from 'sonner';
import { useState } from 'react';
import useUserLocation from '@/hooks/useUserLocation';

interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface FoodCardProps {
  id: string;
  name: string;
  slug?: string;
  vendor: string;
  vendorId: string; // Added vendorId property
  price: number;
  rating: number;
  reviewCount: number;
  image: string;
  description?: string;
  category?: string;
  subcategories?: Subcategory[]; // Added subcategories property
}

export default function FoodCard(props: FoodCardProps) {
  const router = useRouter();
  const { addToCart, updateCartItem, cart } = useCart();
  const { isAuthenticated } = useAuth();
  const { coordinates } = useUserLocation();
  const [imageError, setImageError] = useState(false);

  // Check if item is in cart and get its quantity
  const cartItem = cart.items.find(item => item.menuItemId === props.id);
  const isInCart = !!cartItem;
  const quantity = cartItem?.quantity || 0;

  // Get the default subcategory (first one) or create a fallback
  const getDefaultSubcategory = (): Subcategory => {
    if (props.subcategories && props.subcategories.length > 0) {
      return props.subcategories[0];
    }
    // Fallback if no subcategories provided
    return {
      title: "unit",
      quantity: "unit",
      price: props.price
    };
  };

  const handleNavigate = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Use slug if available, otherwise fall back to ID
    const identifier = props.slug || props.id;
    router.push(`/items/${identifier}`);
  };

  const handleIncreaseQuantity = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      router.push('/login');
      return;
    }

    if (isInCart) {
      // Get the current subcategory from cart or use default
      const currentSubcategory = cartItem?.selectedSubcategory || getDefaultSubcategory();
      updateCartItem(props.id, quantity + 1, '', currentSubcategory);
    } else {
      handleAddToCart(e);
    }
  };

  const handleDecreaseQuantity = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (quantity > 1) {
      // Get the current subcategory from cart or use default
      const currentSubcategory = cartItem?.selectedSubcategory || getDefaultSubcategory();
      updateCartItem(props.id, quantity - 1, '', currentSubcategory);
    } else {
      // Get the current subcategory from cart or use default
      const currentSubcategory = cartItem?.selectedSubcategory || getDefaultSubcategory();
      updateCartItem(props.id, 0, '', currentSubcategory); // This will remove the item
    }
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      router.push('/login');
      return;
    }

    try {
      // Ensure all required fields are present
      if (!props.id || !props.name || !props.price || !props.image || !props.vendor || !props.vendorId) {
        console.error('Missing required properties for adding to cart:', props);
        return;
      }

      // Update the local storage cart with item details before calling addToCart
      // This ensures the item details are available for display in the cart
      try {
        const localCartKey = 'bandiwala-cart';
        const localCartJson = localStorage.getItem(localCartKey);
        const localCart = localCartJson ? JSON.parse(localCartJson) : { userId: '', items: [] };

        // Find if the item already exists in the cart
        const existingItemIndex = localCart.items.findIndex(
          (cartItem: any) => cartItem.menuItemId === props.id
        );

        // If the item doesn't exist in the cart, add its details to local storage
        if (existingItemIndex === -1) {
          // Store item details in local storage for display purposes
          const itemDetails = {
            menuItemId: props.id,
            name: props.name,
            price: props.price,
            image: props.image,
            vendorId: props.vendorId,
            vendorName: props.vendor
          };

          // Save these details in a separate storage key for reference
          localStorage.setItem(`item-details-${props.id}`, JSON.stringify(itemDetails));
        }
      } catch (storageError) {
        console.error('Error updating local storage:', storageError);
      }

      // Store item details in local storage for reference
      const itemDetails = {
        menuItemId: props.id,
        name: props.name,
        price: props.price,
        image: props.image,
        vendorId: props.vendorId,
        vendorName: props.vendor
      };

      localStorage.setItem(`item-details-${props.id}`, JSON.stringify(itemDetails));

      // Use the actual subcategory data from the menu item
      const defaultSubcategory = getDefaultSubcategory();

      // Call addToCart with the correct parameters including user location
      addToCart(props.id, 1, '', defaultSubcategory, coordinates || undefined);
    } catch (error) {
      console.error('Error adding item to cart:', error);
      toast("Failed to add item to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
    }
  };

  return <Card key={props.id} className="overflow-hidden card-hover border-gray-300" onClick={handleNavigate}>
    <div className="relative h-36 sm:h-48">
      <Image
        src={imageError ? '/images/vendor1.jpg' : getImageUrl(props.image || '/images/vendor1.jpg')}
        alt={props.name}
        className="w-full h-full object-cover"
        width={400}
        height={300}
        onError={() => setImageError(true)}
      />
      <div className="absolute top-1 left-1 sm:top-2 sm:left-2 bg-white px-2 py-1 sm:px-3 rounded-full text-xs font-bold text-bandiwala-red">
        {props.category}
      </div>
    </div>
    <CardContent className="p-3 sm:p-4">
      <h3 className="font-semibold text-sm sm:text-lg truncate">{props.name}</h3>
      <p className="text-gray-500 text-xs sm:text-sm truncate">{props.vendor}</p>

      <div className="flex justify-between items-center mt-2 sm:mt-4">
        <div className="flex items-center">
          <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-yellow-400 text-yellow-400 mr-1" />
          <span className="text-xs sm:text-sm font-medium">{props.rating}</span>
          <span className="text-xs text-gray-500 ml-1 hidden sm:inline">({props.reviewCount} reviews)</span>
        </div>

        <p className="font-semibold text-sm sm:text-base">₹{props.price}</p>
      </div>

      {isInCart ? (
        <div className="mt-3 sm:mt-4 flex items-center gap-2">
          <div className="flex items-center bg-gray-100 border border-gray-200 rounded-full py-1.5 sm:py-2 px-3 sm:px-4">
            <button
              onClick={handleDecreaseQuantity}
              className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 hover:bg-gray-200 rounded-full transition-colors"
            >
              <Minus className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
            </button>
            <span className="font-semibold text-center min-w-[24px] sm:min-w-[28px] text-sm sm:text-base text-gray-800">{quantity}</span>
            <button
              onClick={handleIncreaseQuantity}
              className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 hover:bg-gray-200 rounded-full transition-colors"
            >
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
            </button>
          </div>
          <button
            className="flex-1 bg-bandiwala-orange hover:bg-bandiwala-red text-white rounded-full py-1.5 sm:py-2 text-sm sm:text-base font-medium transition-colors flex items-center justify-center"
            onClick={handleAddToCart}
          >
            <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Add to Cart</span>
            <span className="sm:hidden">Add</span>
          </button>
        </div>
      ) : (
        <button
          className="mt-3 sm:mt-4 w-full bg-bandiwala-orange hover:bg-bandiwala-red text-white rounded-full py-1.5 sm:py-2 text-sm sm:text-base font-medium transition-colors flex items-center justify-center"
          onClick={handleAddToCart}
        >
          <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
          <span className="hidden sm:inline">Add to Cart</span>
          <span className="sm:hidden">Add</span>
        </button>
      )}
    </CardContent>
  </Card>
}
